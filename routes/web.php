<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ContactController; // Added for Lokus MVP
use App\Http\Controllers\PropertyController; // Added for Lokus MVP
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/settings/profile', [ProfileController::class, 'edit'])->name('settings.profile');
});


// Authenticated routes for listers
Route::middleware(['auth', /*'verified',*/ 'role:lister'])->group(function () {
    Route::get('/properties/create', [PropertyController::class, 'create'])->name('properties.create');
    Route::get('/my-properties', [PropertyController::class, 'myProperties'])->name('properties.my');
    Route::post('/properties', [PropertyController::class, 'store'])->name('properties.store');
    Route::get('/properties/{property}/edit', [PropertyController::class, 'edit'])->name('properties.edit');
    Route::put('/properties/{property}', [PropertyController::class, 'update'])->name('properties.update');
    Route::delete('/properties/{property}', [PropertyController::class, 'destroy'])->name('properties.destroy');
    Route::patch('/properties/{property}/status', [PropertyController::class, 'updateStatus'])->name('properties.updateStatus'); // For marking as sold/rented
});

// Admin Routes - Commented out to avoid conflict with Filament admin panel
// Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin')->name('admin.')->group(function () {
//     Route::get('/', [\App\Http\Controllers\AdminController::class, 'index'])->name('dashboard');
//
//     // User Management
//     Route::get('/users', \App\Livewire\Admin\UserManagement::class)->name('users.index');

//     // Property Management
//     Route::get('/properties', [\App\Http\Controllers\AdminController::class, 'viewProperties'])->name('properties.index');
//     Route::patch('/properties/{property}/update-status', [\App\Http\Controllers\AdminController::class, 'updatePropertyStatus'])->name('properties.updateStatus');
//     Route::get('/properties/{property}/edit', [\App\Http\Controllers\AdminController::class, 'editProperty'])->name('properties.edit');
//     Route::put('/properties/{property}', [\App\Http\Controllers\AdminController::class, 'updateProperty'])->name('properties.update');
//     Route::delete('/properties/{property}', [\App\Http\Controllers\AdminController::class, 'destroyProperty'])->name('properties.destroy');
// });


// Public routes for property search and display
Route::get('/properties', [PropertyController::class, 'index'])->name('properties.index');
Route::get('/properties/{property}', [PropertyController::class, 'show'])->name('properties.show');


require __DIR__.'/auth.php';
